<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>623</width>
    <height>252</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="featuresOne_label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255)</string>
   </property>
   <property name="text">
    <string>S S R：</string>
   </property>
  </widget>
  <widget class="QLabel" name="featuresTwo_label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>50</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255)</string>
   </property>
   <property name="text">
    <string>C T：</string>
   </property>
  </widget>
  <widget class="QLabel" name="featuresThree_label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>90</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255)</string>
   </property>
   <property name="text">
    <string>C H T：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresOne_edit">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>10</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresTwo_edit">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>50</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresThree_edit">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>90</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLabel" name="featuresFour_label">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>10</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255)</string>
   </property>
   <property name="text">
    <string>R R：</string>
   </property>
  </widget>
  <widget class="QLabel" name="featuresFive_label">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>50</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255);</string>
   </property>
   <property name="text">
    <string>S i O 2 %：</string>
   </property>
  </widget>
  <widget class="QLabel" name="featuresSix_label">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>90</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">ground-back:rgb(0, 85, 255)</string>
   </property>
   <property name="text">
    <string>A l 2 O 3：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresFour_edit">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>10</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresFive_edit">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>50</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="featuresSix_edit">
   <property name="geometry">
    <rect>
     <x>280</x>
     <y>90</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QPushButton" name="forecasting_button">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>20</y>
     <width>200</width>
     <height>200</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>200</width>
     <height>200</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>200</width>
     <height>200</height>
    </size>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>18</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
background-color:rgb(255, 255, 255);
border-radius: 20px;
border:2px groove gray;
border-style:outset;
}</string>
   </property>
   <property name="text">
    <string>Predict</string>
   </property>
   <property name="autoRepeat">
    <bool>false</bool>
   </property>
   <property name="autoExclusive">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QLabel" name="predictEnd_label">
   <property name="geometry">
    <rect>
     <x>13</x>
     <y>150</y>
     <width>101</width>
     <height>81</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>16</pointsize>
    </font>
   </property>
   <property name="text">
    <string>预测结果：</string>
   </property>
  </widget>
  <widget class="QLabel" name="message">
   <property name="geometry">
    <rect>
     <x>123</x>
     <y>141</y>
     <width>271</width>
     <height>101</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>14</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background:rgb(0, 0, 0);
color:rgb(255, 255, 255);
border-radius: 10px;</string>
   </property>
   <property name="text">
    <string>此处显示预测结果</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
