<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>568</width>
    <height>368</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="inputMethodHints">
   <set>Qt::ImhNone</set>
  </property>
  <widget class="QLabel" name="usernameLabel">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>39</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>账    户：</string>
   </property>
  </widget>
  <widget class="QLabel" name="passwordLabel">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>79</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>密    码：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="usernameEdit">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>39</y>
     <width>181</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLineEdit" name="passwordEdit">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>79</y>
     <width>181</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="codeLabel">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>119</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>验 证 码：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="codeEdit">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>119</y>
     <width>181</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="loginButton">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>159</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color:rgb(89, 164, 255)</string>
   </property>
   <property name="text">
    <string>登录</string>
   </property>
   <property name="autoDefault">
    <bool>false</bool>
   </property>
   <property name="default">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QLabel" name="messagelabel">
   <property name="geometry">
    <rect>
     <x>243</x>
     <y>0</y>
     <width>321</width>
     <height>191</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color:rgb(0,0,0);
color:rgb(255,255,255)</string>
   </property>
   <property name="text">
    <string>这是一个状态消息框</string>
   </property>
  </widget>
  <widget class="QComboBox" name="countyBox">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>210</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <item>
    <property name="text">
     <string>北京</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="countylabel">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>210</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>区县：</string>
   </property>
  </widget>
  <widget class="QLabel" name="subjectlabel">
   <property name="geometry">
    <rect>
     <x>340</x>
     <y>210</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>报考科目：</string>
   </property>
  </widget>
  <widget class="QComboBox" name="subjectBox">
   <property name="geometry">
    <rect>
     <x>400</x>
     <y>210</y>
     <width>151</width>
     <height>31</height>
    </rect>
   </property>
   <item>
    <property name="text">
     <string>管理经济学</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="disciplinelabel">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>220</y>
     <width>54</width>
     <height>12</height>
    </rect>
   </property>
   <property name="text">
    <string>所属专业：</string>
   </property>
  </widget>
  <widget class="QComboBox" name="disciplineBox">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>210</y>
     <width>141</width>
     <height>31</height>
    </rect>
   </property>
   <item>
    <property name="text">
     <string>计算机信息管理</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="registrationNoLabel">
   <property name="geometry">
    <rect>
     <x>3</x>
     <y>0</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>准考证号：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="registrationNoEdit">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>0</y>
     <width>181</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="loginButton_2">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>260</y>
     <width>491</width>
     <height>61</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color:rgb(89, 164, 255)</string>
   </property>
   <property name="text">
    <string>开始报考</string>
   </property>
   <property name="autoDefault">
    <bool>false</bool>
   </property>
   <property name="default">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="flushCodeButton">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>160</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color:rgb(89, 164, 255)</string>
   </property>
   <property name="text">
    <string>刷新验证码</string>
   </property>
   <property name="autoDefault">
    <bool>false</bool>
   </property>
   <property name="default">
    <bool>false</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
