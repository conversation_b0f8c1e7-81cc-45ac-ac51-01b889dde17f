# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'sendData.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.
from time import sleep

from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap, QFont
import sys, os

from PyQt5.QtWidgets import QScrollArea, QVBoxLayout

from sendData import postLogin, getPicture, sendData
class Ui_MainWindow(object):

    def __init__(self):
        self.picture = getPicture()
        self.BASE_DIR = os.path.dirname(os.path.realpath(sys.argv[0]))
        self.msg_history = []

    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(800, 300)

        self.qhlay_1 = QtWidgets.QWidget(MainWindow)
        self.qhlay_1.setObjectName("qhlay_1")

        # 配置用户名
        ## 配置用户名标签
        self.label_username = QtWidgets.QLabel(self.qhlay_1)
        self.label_username.setGeometry(QtCore.QRect(10, 10, 71, 41))
        self.label_username.setObjectName("label_username")
        ## 配置用户名文本框
        self.text_username = QtWidgets.QLineEdit(self.qhlay_1)
        self.text_username.setGeometry(QtCore.QRect(90, 10, 241, 41))
        # self.text_username.viewport().setProperty("cursor", QtGui.QCursor(QtCore.Qt.IBeamCursor))
        self.text_username.setProperty("cursor", QtGui.QCursor(QtCore.Qt.IBeamCursor))
        ft = QFont()
        ft.setPointSize(12)
        ft.setFamily("SimSun")
        self.text_username.setFont(ft)
        self.text_username.setObjectName("text_username")

        # 配置登录按钮
        self.button_login = QtWidgets.QPushButton(self.qhlay_1)
        self.button_login.setGeometry(QtCore.QRect(10, 170, 151, 41))
        self.button_login.setObjectName("button_login")
        # 配置密码
        ## 配置密码标签
        self.label_password = QtWidgets.QLabel(self.qhlay_1)
        self.label_password.setGeometry(QtCore.QRect(10, 60, 71, 41))
        self.label_password.setObjectName("label_password")
        ## 配置密码文本框
        self.text_password = QtWidgets.QLineEdit(self.qhlay_1)
        self.text_password.setGeometry(QtCore.QRect(90, 60, 241, 41))
        self.text_password.setProperty("cursor", QtGui.QCursor(QtCore.Qt.IBeamCursor))   # 配置密码框鼠标

        self.text_password.setFont(ft)
        self.text_password.setObjectName("text_password")

        # 配置发送数据按钮
        self.pushButton = QtWidgets.QPushButton(self.qhlay_1)
        self.pushButton.setGeometry(QtCore.QRect(180, 170, 151, 41))
        self.pushButton.setObjectName("pushButton")


        # 配置消息显示框
        self.label_msg = QtWidgets.QLabel(self.qhlay_1)
        self.label_msg.setWordWrap(True)
        self.label_msg.setGeometry(QtCore.QRect(353, 10, 441, 201))
        self.label_msg.setAlignment(Qt.AlignTop)

        self.label_msg.setObjectName("")
        ## 配置图片加载
        pix = QPixmap(os.path.join(self.BASE_DIR, 'utils\\temp.gif'))
        width = pix.width()  ##获取图片宽度
        height = pix.height()  ##获取图片高度
        if width / self.label_msg.width() >= height / self.label_msg.height():  ##比较图片宽度与label宽度之比和图片高度与label高度之比
            ratio = width / self.label_msg.width()
        else:
            ratio = height / self.label_msg.height()

        self.label_msg.setPixmap(pix.scaled(width / ratio, height / ratio))

        ## 配置文字字体大小
        ft.setPointSize(14)
        ft.setFamily("SimSun")
        self.label_msg.setFont(ft)

        # 创建一个滚动对象
        self.scroll_msg = QScrollArea(self.qhlay_1)
        self.scroll_msg.setWidget(self.label_msg)
        self.scroll_msg.setGeometry(QtCore.QRect(353, 10, 441, 201))
        self.label_msg.setAlignment(Qt.AlignTop)
        v_layout = QVBoxLayout()
        v_layout.addWidget(self.scroll_msg)

        # 配置验证码
        ## 配置验证码标签
        self.label_code = QtWidgets.QLabel(self.qhlay_1)
        self.label_code.setGeometry(QtCore.QRect(10, 110, 71, 41))
        self.label_code.setObjectName("label_code")
        ## 配置验证码文本框
        self.text_code = QtWidgets.QLineEdit(self.qhlay_1)
        self.text_code.setGeometry(QtCore.QRect(90, 110, 241, 41))
        self.text_code.setProperty("cursor", QtGui.QCursor(QtCore.Qt.IBeamCursor))
        ft.setPointSize(12)
        ft.setFamily("SimSun")
        self.text_code.setFont(ft)
        self.text_code.setObjectName("text_code")

        self.label_username.raise_()
        self.label_password.raise_()
        self.text_username.raise_()
        self.button_login.raise_()
        self.label_password.raise_()
        self.text_password.raise_()
        self.pushButton.raise_()
        self.label_code.raise_()
        self.text_code.raise_()
        self.label_msg.raise_()
        MainWindow.setCentralWidget(self.qhlay_1)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 800, 22))
        self.menubar.setObjectName("menubar")
        MainWindow.setMenuBar(self.menubar)
        self.statusBar = QtWidgets.QStatusBar(MainWindow)
        self.statusBar.setObjectName("statusBar")
        MainWindow.setStatusBar(self.statusBar)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)


    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.label_username.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">用户名：</span></p></body></html>"))
        self.button_login.setText(_translate("MainWindow", "登录"))
        self.label_password.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">密 码：</span></p></body></html>"))
        self.pushButton.setText(_translate("MainWindow", "发送数据"))
        self.label_code.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" font-size:12pt;\">验证码：</span></p></body></html>"))

        # 绑定登录事件
        self.button_login.clicked.connect(self.login01)
        self.pushButton.clicked.connect(self.SendData)

    def login01(self):
        """
        登录按钮的槽函数
        """
        login_msg = postLogin(picture_uuid=self.picture[0], username=self.text_username.text(), password=self.text_password.text(),
                                  code=self.text_code.text())

        self.login_token = login_msg[0]
        self.label_msg.setText(login_msg[1])  # 发送数据
        self.label_msg.repaint() # 重新绘制数据

    def SendData(self):
        data = sendData(token=self.login_token)
        ft = QFont()
        ft.setPointSize(9)
        ft.setFamily("SimSun")
        self.label_msg.setFont(ft)
        for i in data:
            self.msg_history.append(i)
            self.label_msg.setText("<br>".join(self.msg_history))
            self.label_msg.resize(441, self.label_msg.frameSize().height()+30)
            self.label_msg.repaint()
            sleep(0.1)


