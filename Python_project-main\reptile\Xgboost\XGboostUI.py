# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'XGboost2.0.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(623, 252)
        self.featuresOne_label = QtWidgets.QLabel(Form)
        self.featuresOne_label.setGeometry(QtCore.QRect(10, 10, 61, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresOne_label.setFont(font)
        self.featuresOne_label.setAutoFillBackground(False)
        self.featuresOne_label.setStyleSheet("ground-back:rgb(0, 85, 255)")
        self.featuresOne_label.setObjectName("featuresOne_label")
        self.featuresTwo_label = QtWidgets.QLabel(Form)
        self.featuresTwo_label.setGeometry(QtCore.QRect(10, 50, 61, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresTwo_label.setFont(font)
        self.featuresTwo_label.setAutoFillBackground(False)
        self.featuresTwo_label.setStyleSheet("ground-back:rgb(0, 85, 255)")
        self.featuresTwo_label.setObjectName("featuresTwo_label")
        self.featuresThree_label = QtWidgets.QLabel(Form)
        self.featuresThree_label.setGeometry(QtCore.QRect(10, 90, 61, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresThree_label.setFont(font)
        self.featuresThree_label.setAutoFillBackground(False)
        self.featuresThree_label.setStyleSheet("ground-back:rgb(0, 85, 255)")
        self.featuresThree_label.setObjectName("featuresThree_label")
        self.featuresOne_edit = QtWidgets.QLineEdit(Form)
        self.featuresOne_edit.setGeometry(QtCore.QRect(80, 10, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresOne_edit.setFont(font)
        self.featuresOne_edit.setObjectName("featuresOne_edit")
        self.featuresTwo_edit = QtWidgets.QLineEdit(Form)
        self.featuresTwo_edit.setGeometry(QtCore.QRect(80, 50, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresTwo_edit.setFont(font)
        self.featuresTwo_edit.setObjectName("featuresTwo_edit")
        self.featuresThree_edit = QtWidgets.QLineEdit(Form)
        self.featuresThree_edit.setGeometry(QtCore.QRect(80, 90, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresThree_edit.setFont(font)
        self.featuresThree_edit.setObjectName("featuresThree_edit")
        self.featuresFour_label = QtWidgets.QLabel(Form)
        self.featuresFour_label.setGeometry(QtCore.QRect(200, 10, 81, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresFour_label.setFont(font)
        self.featuresFour_label.setAutoFillBackground(False)
        self.featuresFour_label.setStyleSheet("ground-back:rgb(0, 85, 255)")
        self.featuresFour_label.setObjectName("featuresFour_label")
        self.featuresFive_label = QtWidgets.QLabel(Form)
        self.featuresFive_label.setGeometry(QtCore.QRect(200, 50, 81, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresFive_label.setFont(font)
        self.featuresFive_label.setAutoFillBackground(False)
        self.featuresFive_label.setStyleSheet("ground-back:rgb(0, 85, 255);")
        self.featuresFive_label.setObjectName("featuresFive_label")
        self.featuresSix_label = QtWidgets.QLabel(Form)
        self.featuresSix_label.setGeometry(QtCore.QRect(200, 90, 81, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.featuresSix_label.setFont(font)
        self.featuresSix_label.setAutoFillBackground(False)
        self.featuresSix_label.setStyleSheet("ground-back:rgb(0, 85, 255)")
        self.featuresSix_label.setObjectName("featuresSix_label")
        self.featuresFour_edit = QtWidgets.QLineEdit(Form)
        self.featuresFour_edit.setGeometry(QtCore.QRect(280, 10, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresFour_edit.setFont(font)
        self.featuresFour_edit.setObjectName("featuresFour_edit")
        self.featuresFive_edit = QtWidgets.QLineEdit(Form)
        self.featuresFive_edit.setGeometry(QtCore.QRect(280, 50, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresFive_edit.setFont(font)
        self.featuresFive_edit.setObjectName("featuresFive_edit")
        self.featuresSix_edit = QtWidgets.QLineEdit(Form)
        self.featuresSix_edit.setGeometry(QtCore.QRect(280, 90, 111, 31))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.featuresSix_edit.setFont(font)
        self.featuresSix_edit.setObjectName("featuresSix_edit")
        self.forecasting_button = QtWidgets.QPushButton(Form)
        self.forecasting_button.setGeometry(QtCore.QRect(410, 20, 200, 200))
        self.forecasting_button.setMinimumSize(QtCore.QSize(200, 200))
        self.forecasting_button.setMaximumSize(QtCore.QSize(200, 200))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(18)
        self.forecasting_button.setFont(font)
        self.forecasting_button.setStyleSheet("QPushButton{\n"
                                              "background-color:rgb(255, 255, 255);\n"
                                              "border-radius: 20px;\n"
                                              "border:2px groove gray;\n"
                                              "border-style:outset;\n"
                                              "}")
        self.forecasting_button.setAutoRepeat(False)
        self.forecasting_button.setAutoExclusive(False)
        self.forecasting_button.setObjectName("forecasting_button")
        self.predictEnd_label = QtWidgets.QLabel(Form)
        self.predictEnd_label.setGeometry(QtCore.QRect(13, 150, 101, 81))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(16)
        self.predictEnd_label.setFont(font)
        self.predictEnd_label.setObjectName("predictEnd_label")
        self.message = QtWidgets.QLabel(Form)
        self.message.setGeometry(QtCore.QRect(123, 141, 271, 101))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(14)
        self.message.setFont(font)
        self.message.setStyleSheet("background:rgb(0, 0, 0);\n"
                                   "color:rgb(255, 255, 255);\n"
                                   "border-radius: 10px;")
        self.message.setObjectName("message")

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.featuresOne_label.setText(_translate("Form", "S S R："))
        self.featuresTwo_label.setText(_translate("Form", "C T："))
        self.featuresThree_label.setText(_translate("Form", "C H T："))
        self.featuresFour_label.setText(_translate("Form", "R R："))
        self.featuresFive_label.setText(_translate("Form", "S i O <sub>2</sub> %："))
        self.featuresSix_label.setText(_translate("Form", "A l  <sub>2</sub> O  <sub>3</sub>："))
        self.forecasting_button.setText(_translate("Form", "Predict"))
        self.predictEnd_label.setText(_translate("Form", "预测结果："))
        self.message.setText(_translate("Form", "此处显示预测结果"))
