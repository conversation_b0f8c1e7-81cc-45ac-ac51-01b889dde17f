import datetime
import time


def 三猫产品插入():
    d = {
        "cutPage": {
            "AllCount": 8,
            "AllPage": 1,
            "EveryPage": 20,
            "NowPage": 1,
            "BeginCount": 0
        },
        "goodsList": [
            {
                "Gid": 5072,
                "MainId": 8,
                "Name": "【临期卡密】爱奇艺黄金会员周卡 (质保1小时）",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 5039,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "33572",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 6.54,
                "One": 6.74,
                "Two": 6.84,
                "Three": 7.04,
                "Four": 7.24,
                "Five": 6.54,
                "Six": 6.54,
                "Seven": 6.54,
                "Eight": 6.54,
                "Nine": 6.54,
                "Ten": 6.54,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "https://img2.baidu.com/it/u=3588222065,4033279896&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 6.84,
                "PriceB": 6.74,
                "PriceC": 6.64,
                "MoneyId": 4,
                "SalePrice": 6.84,
                "OldSalePrice": 0,
                "VipStockPrice": 6.64,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 5067,
                "MainId": 8,
                "Name": "【共享】芒果会员月号（只限安卓手机使用）",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 5034,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "33507",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 2.05,
                "One": 2.25,
                "Two": 2.35,
                "Three": 2.55,
                "Four": 2.75,
                "Five": 2.05,
                "Six": 2.05,
                "Seven": 2.05,
                "Eight": 2.05,
                "Nine": 2.05,
                "Ten": 2.05,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/fdca26edd9ae405b98660f90b4ba1da6.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 2.35,
                "PriceB": 2.25,
                "PriceC": 2.15,
                "MoneyId": 4,
                "SalePrice": 2.35,
                "OldSalePrice": 0,
                "VipStockPrice": 2.15,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 3423,
                "MainId": 8,
                "Name": "【共享】优酷会员25-31天【稳定版】",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 3391,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "18174",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 3.05,
                "One": 3.25,
                "Two": 3.35,
                "Three": 3.55,
                "Four": 3.75,
                "Five": 3.05,
                "Six": 3.05,
                "Seven": 3.05,
                "Eight": 3.05,
                "Nine": 3.05,
                "Ten": 3.05,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/e40026b2614445a5a7d921428f778bf2.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 3.35,
                "PriceB": 3.25,
                "PriceC": 3.15,
                "MoneyId": 4,
                "SalePrice": 3.35,
                "OldSalePrice": 0,
                "VipStockPrice": 3.15,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 3422,
                "MainId": 8,
                "Name": "【共享】优酷会员25-31天 账号密码登陆",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 3390,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "17788",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 2.55,
                "One": 2.75,
                "Two": 2.85,
                "Three": 3.05,
                "Four": 3.25,
                "Five": 2.55,
                "Six": 2.55,
                "Seven": 2.55,
                "Eight": 2.55,
                "Nine": 2.55,
                "Ten": 2.55,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/e40026b2614445a5a7d921428f778bf2.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 2.85,
                "PriceB": 2.75,
                "PriceC": 2.65,
                "MoneyId": 4,
                "SalePrice": 2.85,
                "OldSalePrice": 0,
                "VipStockPrice": 2.65,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 1132,
                "MainId": 8,
                "Name": "移动100元",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 3,
                "TemplateId": 0,
                "GoodsNo": 1100,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "29375",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 93.28,
                "One": 93.48,
                "Two": 93.58,
                "Three": 93.78,
                "Four": 93.98,
                "Five": 93.28,
                "Six": 93.28,
                "Seven": 93.28,
                "Eight": 93.28,
                "Nine": 93.28,
                "Ten": 93.28,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "https://img0.baidu.com/it/u=2479442951,755245626&fm=253&fmt=auto&app=138&f=JPEG?w=380&h=380",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 93.58,
                "PriceB": 93.48,
                "PriceC": 93.38,
                "MoneyId": 4,
                "SalePrice": 93.58,
                "OldSalePrice": 0,
                "VipStockPrice": 93.38,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 2,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 298,
                "MainId": 8,
                "Name": "【共享电视专用】云视听极光电视会员25-30天【稳定版】",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 266,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "24699",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 6.58,
                "One": 6.78,
                "Two": 6.88,
                "Three": 7.08,
                "Four": 7.28,
                "Five": 6.58,
                "Six": 6.58,
                "Seven": 6.58,
                "Eight": 6.58,
                "Nine": 6.58,
                "Ten": 6.58,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/dc5aac3f95c14de08246c433858051a4.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 6.88,
                "PriceB": 6.78,
                "PriceC": 6.68,
                "MoneyId": 4,
                "SalePrice": 6.88,
                "OldSalePrice": 0,
                "VipStockPrice": 6.68,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 2,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 287,
                "MainId": 8,
                "Name": "【共享】芒果 25-31天 仅电脑使用",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 255,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "256",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 2.05,
                "One": 2.25,
                "Two": 2.35,
                "Three": 2.55,
                "Four": 2.75,
                "Five": 2.05,
                "Six": 2.05,
                "Seven": 2.05,
                "Eight": 2.05,
                "Nine": 2.05,
                "Ten": 2.05,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/fdca26edd9ae405b98660f90b4ba1da6.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": "",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 2.35,
                "PriceB": 2.25,
                "PriceC": 2.15,
                "MoneyId": 4,
                "SalePrice": 2.35,
                "OldSalePrice": 0,
                "VipStockPrice": 2.15,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            },
            {
                "Gid": 286,
                "MainId": 8,
                "Name": "【共享】芒果 25-31天（平板下载芒果HD版使用）",
                "FaceValue": 0,
                "Introduction": "",
                "TitleStyle": "",
                "AddTime": "0001-01-01T00:00:00Z",
                "Type": 1,
                "TemplateId": 0,
                "GoodsNo": 254,
                "SourceId": 0,
                "OutId": 0,
                "OutGoodsId": "254",
                "State": 1,
                "UserId": 0,
                "GoodsOrder": 0,
                "IsHot": 0,
                "IsCommon": 0,
                "CheckState": 0,
                "RateValue": 0,
                "MaxCount": 0,
                "MinCount": 0,
                "StockPrice": 2.05,
                "One": 2.25,
                "Two": 2.35,
                "Three": 2.55,
                "Four": 2.75,
                "Five": 2.05,
                "Six": 2.05,
                "Seven": 2.05,
                "Eight": 2.05,
                "Nine": 2.05,
                "Ten": 2.05,
                "IsHuo": 0,
                "UserNo": 0,
                "SaleCount": 0,
                "IsPrice": 0,
                "EffDays": 0,
                "StopReason": "",
                "Apiid": 2,
                "ApiCount": 0,
                "DeliverCount": 0,
                "Img": "http://www.6cdk.cn/attached/images/10258/public/fdca26edd9ae405b98660f90b4ba1da6.png",
                "ApiMintues": 0,
                "ApiGoodsNo": "",
                "TemplateType": 0,
                "SellCount": 0,
                "DayCount": 0,
                "IsTuiDan": 0,
                "IsTuiKuan": 0,
                "Mode": 0,
                "OpenBuyId": 0,
                "OpenOrderId": 0,
                "OpenNotifyId": 0,
                "Tips": "",
                "Describe": " 售后：25天",
                "DescribeColor": "",
                "TagName": "",
                "TagColor": "",
                "Top": 0,
                "ConvertStatus": 0,
                "ConvertType": 0,
                "ConvertTitle": "",
                "ConvertDetail": "",
                "IsRepeat": 0,
                "PriceWhite": 0,
                "WhiteMsg": "",
                "DeliverType": 0,
                "DeliverId": 0,
                "FixedStatus": 0,
                "FixedText": "",
                "FixedId": 0,
                "FixedCpkId": "",
                "ChannelStatus": 0,
                "ChannelText": "",
                "SkuType": 0,
                "StockType": 0,
                "StockCount": 0,
                "TicalStatus": 0,
                "TicalDay": 0,
                "TicalType": 0,
                "TicalDetail": "",
                "PriceA": 2.35,
                "PriceB": 2.25,
                "PriceC": 2.15,
                "MoneyId": 4,
                "SalePrice": 2.35,
                "OldSalePrice": 0,
                "VipStockPrice": 2.15,
                "OldVipStockPrice": 0,
                "TopVipRate": 0,
                "TopVipRateMoney": 0,
                "OldTopVipRateMoney": 0,
                "Content": "",
                "IsHave": 0,
                "ImportCount": 0,
                "Sku": "",
                "SkuStockPrice": 0,
                "Profit": 0,
                "Vid": 0,
                "VMainId": 0,
                "VipId": 0,
                "VGid": 0,
                "VGName": "",
                "VGImg": "",
                "VGContent": "",
                "VGPType": 0,
                "VGPrice": 0,
                "VGState": 0,
                "VGHot": 0,
                "VGTips": "",
                "VGDescribe": "",
                "PriceId": 12,
                "PriceTempName": "",
                "OldId": 0,
                "OldStockPrice": 0,
                "OldOne": 0,
                "OldTwo": 0,
                "OldThree": 0,
                "OldFour": 0,
                "OldFive": 0,
                "OldSix": 0,
                "OldSeven": 0,
                "OldEight": 0,
                "OldNine": 0,
                "OldTen": 0,
                "OldPriceA": 0,
                "OldPriceB": 0,
                "OldPriceC": 0,
                "OldUpdateTime": "0001-01-01T00:00:00Z",
                "Subscribe": 1,
                "SubscribeAgiso": 0,
                "SubscribeGoofish": 0,
                "ToGuid": "",
                "GoofishUrl": "",
                "IsUserPrice": False,
                "OldIsUserPrice": False,
                "LevelName": "",
                "LevelRank": 0,
                "LevelQueryRank": 0,
                "UserVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "TopVip": {
                    "Id": 10,
                    "MainId": 8,
                    "UserId": 78,
                    "UserNo": 10049,
                    "Url": "sm.cykjwl.top",
                    "Comment": "",
                    "AddTime": "2024-10-06T15:39:40+08:00",
                    "Name": "三猫卡券",
                    "State": 1,
                    "StopReasion": "",
                    "CloseTime": {
                        "Time": "0001-01-01T00:00:00Z",
                        "Valid": False
                    },
                    "Version": 3,
                    "CreateUserId": 0,
                    "DefLevelId": 0,
                    "Parent": "主站搭建",
                    "CutMoney": 0,
                    "GroupId": 0,
                    "GroupName": "",
                    "VipId": 0,
                    "VersionName": "",
                    "RateMoney": 0,
                    "TopStatus": 0,
                    "BCount": 0,
                    "EndDay": 0
                },
                "DefaultPriceId": 0,
                "Templates": None,
                "Skus": None,
                "SkuPrices": None,
                "TemplatePrice": {
                    "Id": 0,
                    "MainId": 0,
                    "Tab": 0,
                    "StartPrice": 0,
                    "EndPrice": 0,
                    "ChaPrice": 0,
                    "LevelPrice": 0,
                    "TypeId": 0,
                    "One": 0,
                    "Two": 0,
                    "Three": 0,
                    "Four": 0,
                    "Five": 0,
                    "Six": 0,
                    "Seven": 0,
                    "Eight": 0,
                    "Nine": 0,
                    "Ten": 0,
                    "BName": "",
                    "PriceA": 0,
                    "PriceB": 0,
                    "PriceC": 0,
                    "VipId": 0,
                    "IsDefault": 0,
                    "Levelist": None,
                    "Version": None
                },
                "TemplateRanges": None,
                "OrderSkus": None,
                "FixedSku": "",
                "CostPrice": 0,
                "Code": 0,
                "Msg": "",
                "PrevId": 0,
                "NextId": 0,
                "PrevOrder": 0,
                "NextOrder": 0
            }
        ]
    }
    for i in d.get("goodsList"):
        # print("================")
        sql = f"INSERT INTO `side-hustle`.`sanmao_product_info` (`id`, `sanmao_product_name`, `sale_price`, `priceA`, `priceB`, `priceC`, `status`) " \
              f"VALUES ('{i.get('Gid')}', '{i.get('Name')}', '{i.get('SalePrice')}', '{i.get('PriceA')}', '{i.get('PriceB')}', '{i.get('PriceC')}', '{i.get('State')}');"
        print(sql)


def 闲鱼产品():
    d = {
        "code": 0,
        "msg": "OK",
        "data": {
            "list": [
                {
                    "product_id": 983333971069381,
                    "item_id": 899206574928,
                    "authorize_id": 982963450904645,
                    "publish_id": 983333971069381,
                    "sp_biz_type": 25,
                    "sp_biz_type_text": "卡券",
                    "publish_type": 1,
                    "publish_type_text": "一口价商品",
                    "publish_shop": "天谴之梦",
                    "original_price": 0,
                    "original_price_text": "0.00",
                    "price": 390,
                    "price_text": "3.90",
                    "dist_price": 0,
                    "dist_price_text": "0.00",
                    "dist_sale_price": 0,
                    "dist_sale_price_text": "0.00",
                    "stock": 1,
                    "sold": 0,
                    "status": 22,
                    "status_text": "销售中",
                    "publish_status": 22,
                    "publish_status_text": "销售中",
                    "republish_status": 0,
                    "republish_status_text": "",
                    "timing_publish_status": 0,
                    "timing_publish_type": 0,
                    "timing_publish_interval": 0,
                    "timing_publish_time": "",
                    "timing_publish_time_text": "",
                    "timing_publish_qty": 0,
                    "timing_refresh_qty": 0,
                    "title": "【秒发】芒果HD视频一个月 共享VIP30天【自动发货",
                    "image_url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01wKz15r1dv4u0d6V1S_!!4611686018427386485-53-fleamarket.heic",
                    "idle_src": "http://img.alicdn.com/bao/uploaded/i3/O1CN01wKz15r1dv4u0d6V1S_!!4611686018427386485-53-fleamarket.heic",
                    "outer_id": "",
                    "operating": 0,
                    "bid_start_time": "",
                    "bid_end_time": "",
                    "bid_reserve_price": 0,
                    "bid_reserve_price_text": "0.00",
                    "bid_increase_price": 0,
                    "bid_increase_price_text": "0.00",
                    "bid_deposit": 0,
                    "bid_deposit_text": "0.00",
                    "online_time": "2025-03-17 15:39",
                    "offline_time": "",
                    "sold_time": "",
                    "spec_type": 1,
                    "spec_type_text": "单规格",
                    "source": 12,
                    "source_text": "闲鱼APP",
                    "service_support": "",
                    "specify_publish_time": "",
                    "operate_user": "",
                    "operate_time": "",
                    "create_user": "",
                    "create_time": "2025-03-17 15:39",
                    "item_biz_type": 2,
                    "item_biz_type_text": "普通商品",
                    "recharge_status": 0,
                    "adjust_price_status": 0,
                    "out_sync_item_id": 0,
                    "out_sync_sku_id": 0,
                    "b2c_small_shipper_id": 0,
                    "b2c_small_shipper_name": "",
                    "b2c_big_shipper_id": 0,
                    "fans_price_status": 0,
                    "all_fans_price": 0,
                    "all_fans_price_text": "0.00",
                    "old_fans_price": 0,
                    "old_fans_price_text": "0.00",
                    "buy_fans_price": 0,
                    "buy_fans_price_text": "0.00",
                    "children": [],
                    "sku_item": None,
                    "price_range_text": "3.90",
                    "pre_start_time": 0,
                    "pre_end_time": 0,
                    "pre_out_time": 0,
                    "pre_start_time_text": "",
                    "pre_end_time_text": "",
                    "pre_out_time_text": "",
                    "pre_sale_status": 3,
                    "pay_end_time": 0,
                    "thumbnail": "http://img.alicdn.com/bao/uploaded/i3/O1CN01wKz15r1dv4u0d6V1S_!!4611686018427386485-53-fleamarket.heic_140x10000Q90.jpg_.webp",
                    "images": [
                        "http://img.alicdn.com/bao/uploaded/i3/O1CN01wKz15r1dv4u0d6V1S_!!4611686018427386485-53-fleamarket.heic"
                    ],
                    "bid_item_id": 0,
                    "bid_activity": {
                        "activity_id": 0,
                        "start_time": "",
                        "end_time": "",
                        "remain_time": "",
                        "one_minute": "",
                        "continue_day": 0,
                        "start_price_type": 0,
                        "start_price_value": 0,
                        "first_buy_time": "",
                        "skip_time_start": "",
                        "skip_time_end": "",
                        "activity_name": "",
                        "activity_desc": ""
                    },
                    "bid_num": 1,
                    "bid_items": [
                        {
                            "status_name": "销售中",
                            "bid_item_id": 0,
                            "bid_start_time": "",
                            "bid_end_time": "",
                            "activity_id": 0,
                            "activity_name": "",
                            "one_minute": ""
                        }
                    ],
                    "free_deposit_task": "",
                    "estimated_bid_num": 10,
                    "is_cycle_activity": False
                },
                {
                    "product_id": 983333908564293,
                    "item_id": 899882256339,
                    "authorize_id": 982963450904645,
                    "publish_id": 983333908564293,
                    "sp_biz_type": 25,
                    "sp_biz_type_text": "卡券",
                    "publish_type": 1,
                    "publish_type_text": "一口价商品",
                    "publish_shop": "天谴之梦",
                    "original_price": 1500,
                    "original_price_text": "15.00",
                    "price": 899,
                    "price_text": "8.99",
                    "dist_price": 0,
                    "dist_price_text": "0.00",
                    "dist_sale_price": 0,
                    "dist_sale_price_text": "0.00",
                    "stock": 1,
                    "sold": 0,
                    "status": 22,
                    "status_text": "销售中",
                    "publish_status": 22,
                    "publish_status_text": "销售中",
                    "republish_status": 0,
                    "republish_status_text": "",
                    "timing_publish_status": 0,
                    "timing_publish_type": 0,
                    "timing_publish_interval": 0,
                    "timing_publish_time": "",
                    "timing_publish_time_text": "",
                    "timing_publish_qty": 0,
                    "timing_refresh_qty": 0,
                    "title": "腾讯电视端云视听：云视听极光TV vip月卡Newtv极光",
                    "image_url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01fSDnVf1dv4u21d5ZB_!!4611686018427386485-53-fleamarket.heic",
                    "idle_src": "http://img.alicdn.com/bao/uploaded/i3/O1CN01fSDnVf1dv4u21d5ZB_!!4611686018427386485-53-fleamarket.heic",
                    "outer_id": "",
                    "operating": 0,
                    "bid_start_time": "",
                    "bid_end_time": "",
                    "bid_reserve_price": 0,
                    "bid_reserve_price_text": "0.00",
                    "bid_increase_price": 0,
                    "bid_increase_price_text": "0.00",
                    "bid_deposit": 0,
                    "bid_deposit_text": "0.00",
                    "online_time": "2025-03-17 15:38",
                    "offline_time": "",
                    "sold_time": "",
                    "spec_type": 1,
                    "spec_type_text": "单规格",
                    "source": 12,
                    "source_text": "闲鱼APP",
                    "service_support": "",
                    "specify_publish_time": "",
                    "operate_user": "",
                    "operate_time": "",
                    "create_user": "",
                    "create_time": "2025-03-17 15:38",
                    "item_biz_type": 2,
                    "item_biz_type_text": "普通商品",
                    "recharge_status": 0,
                    "adjust_price_status": 0,
                    "out_sync_item_id": 0,
                    "out_sync_sku_id": 0,
                    "b2c_small_shipper_id": 0,
                    "b2c_small_shipper_name": "",
                    "b2c_big_shipper_id": 0,
                    "fans_price_status": 0,
                    "all_fans_price": 0,
                    "all_fans_price_text": "0.00",
                    "old_fans_price": 0,
                    "old_fans_price_text": "0.00",
                    "buy_fans_price": 0,
                    "buy_fans_price_text": "0.00",
                    "children": [],
                    "sku_item": None,
                    "price_range_text": "8.99",
                    "pre_start_time": 0,
                    "pre_end_time": 0,
                    "pre_out_time": 0,
                    "pre_start_time_text": "",
                    "pre_end_time_text": "",
                    "pre_out_time_text": "",
                    "pre_sale_status": 3,
                    "pay_end_time": 0,
                    "thumbnail": "http://img.alicdn.com/bao/uploaded/i3/O1CN01fSDnVf1dv4u21d5ZB_!!4611686018427386485-53-fleamarket.heic_140x10000Q90.jpg_.webp",
                    "images": [
                        "http://img.alicdn.com/bao/uploaded/i3/O1CN01fSDnVf1dv4u21d5ZB_!!4611686018427386485-53-fleamarket.heic"
                    ],
                    "bid_item_id": 0,
                    "bid_activity": {
                        "activity_id": 0,
                        "start_time": "",
                        "end_time": "",
                        "remain_time": "",
                        "one_minute": "",
                        "continue_day": 0,
                        "start_price_type": 0,
                        "start_price_value": 0,
                        "first_buy_time": "",
                        "skip_time_start": "",
                        "skip_time_end": "",
                        "activity_name": "",
                        "activity_desc": ""
                    },
                    "bid_num": 1,
                    "bid_items": [
                        {
                            "status_name": "销售中",
                            "bid_item_id": 0,
                            "bid_start_time": "",
                            "bid_end_time": "",
                            "activity_id": 0,
                            "activity_name": "",
                            "one_minute": ""
                        }
                    ],
                    "free_deposit_task": "",
                    "estimated_bid_num": 10,
                    "is_cycle_activity": False
                },
                {
                    "product_id": 982981062379525,
                    "item_id": 899890976234,
                    "authorize_id": 982963450904645,
                    "publish_id": 982981062379525,
                    "sp_biz_type": 25,
                    "sp_biz_type_text": "卡券",
                    "publish_type": 1,
                    "publish_type_text": "一口价商品",
                    "publish_shop": "天谴之梦",
                    "original_price": 0,
                    "original_price_text": "0.00",
                    "price": 390,
                    "price_text": "3.90",
                    "dist_price": 0,
                    "dist_price_text": "0.00",
                    "dist_sale_price": 0,
                    "dist_sale_price_text": "0.00",
                    "stock": 1,
                    "sold": 4,
                    "status": 22,
                    "status_text": "销售中",
                    "publish_status": 22,
                    "publish_status_text": "销售中",
                    "republish_status": 0,
                    "republish_status_text": "",
                    "timing_publish_status": 0,
                    "timing_publish_type": 0,
                    "timing_publish_interval": 0,
                    "timing_publish_time": "",
                    "timing_publish_time_text": "",
                    "timing_publish_qty": 0,
                    "timing_refresh_qty": 0,
                    "title": "【秒发】优酷视频一个月 共享SVIP30天【自动发货",
                    "image_url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01kfHi791dv4u0ld3HA_!!4611686018427386485-53-fleamarket.heic",
                    "idle_src": "http://img.alicdn.com/bao/uploaded/i2/O1CN01kfHi791dv4u0ld3HA_!!4611686018427386485-53-fleamarket.heic",
                    "outer_id": "",
                    "operating": 0,
                    "bid_start_time": "",
                    "bid_end_time": "",
                    "bid_reserve_price": 0,
                    "bid_reserve_price_text": "0.00",
                    "bid_increase_price": 0,
                    "bid_increase_price_text": "0.00",
                    "bid_deposit": 0,
                    "bid_deposit_text": "0.00",
                    "online_time": "2025-03-17 15:39",
                    "offline_time": "2025-03-17 09:40",
                    "sold_time": "2025-03-17 09:40",
                    "spec_type": 1,
                    "spec_type_text": "单规格",
                    "source": 12,
                    "source_text": "闲鱼APP",
                    "service_support": "",
                    "specify_publish_time": "",
                    "operate_user": "",
                    "operate_time": "",
                    "create_user": "",
                    "create_time": "2025-03-17 09:40",
                    "item_biz_type": 2,
                    "item_biz_type_text": "普通商品",
                    "recharge_status": 0,
                    "adjust_price_status": 0,
                    "out_sync_item_id": 0,
                    "out_sync_sku_id": 0,
                    "b2c_small_shipper_id": 0,
                    "b2c_small_shipper_name": "",
                    "b2c_big_shipper_id": 0,
                    "fans_price_status": 0,
                    "all_fans_price": 0,
                    "all_fans_price_text": "0.00",
                    "old_fans_price": 0,
                    "old_fans_price_text": "0.00",
                    "buy_fans_price": 0,
                    "buy_fans_price_text": "0.00",
                    "children": [],
                    "sku_item": None,
                    "price_range_text": "3.90",
                    "pre_start_time": 0,
                    "pre_end_time": 0,
                    "pre_out_time": 0,
                    "pre_start_time_text": "",
                    "pre_end_time_text": "",
                    "pre_out_time_text": "",
                    "pre_sale_status": 3,
                    "pay_end_time": 0,
                    "thumbnail": "http://img.alicdn.com/bao/uploaded/i2/O1CN01kfHi791dv4u0ld3HA_!!4611686018427386485-53-fleamarket.heic_140x10000Q90.jpg_.webp",
                    "images": [
                        "http://img.alicdn.com/bao/uploaded/i2/O1CN01kfHi791dv4u0ld3HA_!!4611686018427386485-53-fleamarket.heic"
                    ],
                    "bid_item_id": 0,
                    "bid_activity": {
                        "activity_id": 0,
                        "start_time": "",
                        "end_time": "",
                        "remain_time": "",
                        "one_minute": "",
                        "continue_day": 0,
                        "start_price_type": 0,
                        "start_price_value": 0,
                        "first_buy_time": "",
                        "skip_time_start": "",
                        "skip_time_end": "",
                        "activity_name": "",
                        "activity_desc": ""
                    },
                    "bid_num": 1,
                    "bid_items": [
                        {
                            "status_name": "销售中",
                            "bid_item_id": 0,
                            "bid_start_time": "",
                            "bid_end_time": "",
                            "activity_id": 0,
                            "activity_name": "",
                            "one_minute": ""
                        }
                    ],
                    "free_deposit_task": "",
                    "estimated_bid_num": 10,
                    "is_cycle_activity": False
                },
                {
                    "product_id": 982963465324677,
                    "item_id": 899886600551,
                    "authorize_id": 982963450904645,
                    "publish_id": 982963465324677,
                    "sp_biz_type": 25,
                    "sp_biz_type_text": "卡券",
                    "publish_type": 1,
                    "publish_type_text": "一口价商品",
                    "publish_shop": "天谴之梦",
                    "original_price": 0,
                    "original_price_text": "0.00",
                    "price": 9500,
                    "price_text": "95.00",
                    "dist_price": 0,
                    "dist_price_text": "0.00",
                    "dist_sale_price": 0,
                    "dist_sale_price_text": "0.00",
                    "stock": 1,
                    "sold": 0,
                    "status": 22,
                    "status_text": "销售中",
                    "publish_status": 22,
                    "publish_status_text": "销售中",
                    "republish_status": 0,
                    "republish_status_text": "",
                    "timing_publish_status": 0,
                    "timing_publish_type": 0,
                    "timing_publish_interval": 0,
                    "timing_publish_time": "",
                    "timing_publish_time_text": "",
                    "timing_publish_qty": 0,
                    "timing_refresh_qty": 0,
                    "title": "全国移动联通电信100话费充值",
                    "image_url": "http://img.alicdn.com/bao/uploaded/i2/O1CN012CgvWP1dv4u0tvyae_!!4611686018427386485-53-fleamarket.heic",
                    "idle_src": "http://img.alicdn.com/bao/uploaded/i2/O1CN012CgvWP1dv4u0tvyae_!!4611686018427386485-53-fleamarket.heic",
                    "outer_id": "",
                    "operating": 0,
                    "bid_start_time": "",
                    "bid_end_time": "",
                    "bid_reserve_price": 0,
                    "bid_reserve_price_text": "0.00",
                    "bid_increase_price": 0,
                    "bid_increase_price_text": "0.00",
                    "bid_deposit": 0,
                    "bid_deposit_text": "0.00",
                    "online_time": "2025-03-17 09:22",
                    "offline_time": "",
                    "sold_time": "",
                    "spec_type": 1,
                    "spec_type_text": "单规格",
                    "source": 12,
                    "source_text": "闲鱼APP",
                    "service_support": "",
                    "specify_publish_time": "",
                    "operate_user": "",
                    "operate_time": "",
                    "create_user": "",
                    "create_time": "2025-03-17 09:22",
                    "item_biz_type": 2,
                    "item_biz_type_text": "普通商品",
                    "recharge_status": 0,
                    "adjust_price_status": 0,
                    "out_sync_item_id": 0,
                    "out_sync_sku_id": 0,
                    "b2c_small_shipper_id": 0,
                    "b2c_small_shipper_name": "",
                    "b2c_big_shipper_id": 0,
                    "fans_price_status": 0,
                    "all_fans_price": 0,
                    "all_fans_price_text": "0.00",
                    "old_fans_price": 0,
                    "old_fans_price_text": "0.00",
                    "buy_fans_price": 0,
                    "buy_fans_price_text": "0.00",
                    "children": [],
                    "sku_item": None,
                    "price_range_text": "95.00",
                    "pre_start_time": 0,
                    "pre_end_time": 0,
                    "pre_out_time": 0,
                    "pre_start_time_text": "",
                    "pre_end_time_text": "",
                    "pre_out_time_text": "",
                    "pre_sale_status": 3,
                    "pay_end_time": 0,
                    "thumbnail": "http://img.alicdn.com/bao/uploaded/i2/O1CN012CgvWP1dv4u0tvyae_!!4611686018427386485-53-fleamarket.heic_140x10000Q90.jpg_.webp",
                    "images": [
                        "http://img.alicdn.com/bao/uploaded/i2/O1CN012CgvWP1dv4u0tvyae_!!4611686018427386485-53-fleamarket.heic",
                        "http://img.alicdn.com/bao/uploaded/i1/O1CN015McUFc1dv4u3TaAjY_!!4611686018427386485-53-fleamarket.heic"
                    ],
                    "bid_item_id": 0,
                    "bid_activity": {
                        "activity_id": 0,
                        "start_time": "",
                        "end_time": "",
                        "remain_time": "",
                        "one_minute": "",
                        "continue_day": 0,
                        "start_price_type": 0,
                        "start_price_value": 0,
                        "first_buy_time": "",
                        "skip_time_start": "",
                        "skip_time_end": "",
                        "activity_name": "",
                        "activity_desc": ""
                    },
                    "bid_num": 1,
                    "bid_items": [
                        {
                            "status_name": "销售中",
                            "bid_item_id": 0,
                            "bid_start_time": "",
                            "bid_end_time": "",
                            "activity_id": 0,
                            "activity_name": "",
                            "one_minute": ""
                        }
                    ],
                    "free_deposit_task": "",
                    "estimated_bid_num": 10,
                    "is_cycle_activity": False
                },
                {
                    "product_id": 982963464865861,
                    "item_id": 709338957883,
                    "authorize_id": 982963450904645,
                    "publish_id": 982963464865861,
                    "sp_biz_type": 21,
                    "sp_biz_type_text": "家居",
                    "publish_type": 1,
                    "publish_type_text": "一口价商品",
                    "publish_shop": "天谴之梦",
                    "original_price": 0,
                    "original_price_text": "0.00",
                    "price": 990,
                    "price_text": "9.90",
                    "dist_price": 0,
                    "dist_price_text": "0.00",
                    "dist_sale_price": 0,
                    "dist_sale_price_text": "0.00",
                    "stock": 1,
                    "sold": 0,
                    "status": 22,
                    "status_text": "销售中",
                    "publish_status": 22,
                    "publish_status_text": "销售中",
                    "republish_status": 0,
                    "republish_status_text": "",
                    "timing_publish_status": 0,
                    "timing_publish_type": 0,
                    "timing_publish_interval": 0,
                    "timing_publish_time": "",
                    "timing_publish_time_text": "",
                    "timing_publish_qty": 0,
                    "timing_refresh_qty": 0,
                    "title": "Python代写服务，专业解决各种疑难需求！",
                    "image_url": "http://img.alicdn.com/bao/uploaded/i1/O1CN0186OFg41dv4gPqCnGK_!!53-fleamarket.heic",
                    "idle_src": "http://img.alicdn.com/bao/uploaded/i1/O1CN0186OFg41dv4gPqCnGK_!!53-fleamarket.heic",
                    "outer_id": "",
                    "operating": 0,
                    "bid_start_time": "",
                    "bid_end_time": "",
                    "bid_reserve_price": 0,
                    "bid_reserve_price_text": "0.00",
                    "bid_increase_price": 0,
                    "bid_increase_price_text": "0.00",
                    "bid_deposit": 0,
                    "bid_deposit_text": "0.00",
                    "online_time": "2025-03-17 09:22",
                    "offline_time": "",
                    "sold_time": "",
                    "spec_type": 1,
                    "spec_type_text": "单规格",
                    "source": 12,
                    "source_text": "闲鱼APP",
                    "service_support": "",
                    "specify_publish_time": "",
                    "operate_user": "",
                    "operate_time": "",
                    "create_user": "",
                    "create_time": "2025-03-17 09:22",
                    "item_biz_type": 2,
                    "item_biz_type_text": "普通商品",
                    "recharge_status": 0,
                    "adjust_price_status": 0,
                    "out_sync_item_id": 0,
                    "out_sync_sku_id": 0,
                    "b2c_small_shipper_id": 0,
                    "b2c_small_shipper_name": "",
                    "b2c_big_shipper_id": 0,
                    "fans_price_status": 0,
                    "all_fans_price": 0,
                    "all_fans_price_text": "0.00",
                    "old_fans_price": 0,
                    "old_fans_price_text": "0.00",
                    "buy_fans_price": 0,
                    "buy_fans_price_text": "0.00",
                    "children": [],
                    "sku_item": None,
                    "price_range_text": "9.90",
                    "pre_start_time": 0,
                    "pre_end_time": 0,
                    "pre_out_time": 0,
                    "pre_start_time_text": "",
                    "pre_end_time_text": "",
                    "pre_out_time_text": "",
                    "pre_sale_status": 3,
                    "pay_end_time": 0,
                    "thumbnail": "http://img.alicdn.com/bao/uploaded/i1/O1CN0186OFg41dv4gPqCnGK_!!53-fleamarket.heic_140x10000Q90.jpg_.webp",
                    "images": [
                        "http://img.alicdn.com/bao/uploaded/i1/O1CN0186OFg41dv4gPqCnGK_!!53-fleamarket.heic",
                        "http://img.alicdn.com/bao/uploaded/i3/O1CN01xUtsUo1dv4gcQaVYn_!!53-fleamarket.heic",
                        "http://img.alicdn.com/bao/uploaded/i2/O1CN01IRFm481dv4j1RKmAK_!!53-fleamarket.heic"
                    ],
                    "bid_item_id": 0,
                    "bid_activity": {
                        "activity_id": 0,
                        "start_time": "",
                        "end_time": "",
                        "remain_time": "",
                        "one_minute": "",
                        "continue_day": 0,
                        "start_price_type": 0,
                        "start_price_value": 0,
                        "first_buy_time": "",
                        "skip_time_start": "",
                        "skip_time_end": "",
                        "activity_name": "",
                        "activity_desc": ""
                    },
                    "bid_num": 1,
                    "bid_items": [
                        {
                            "status_name": "销售中",
                            "bid_item_id": 0,
                            "bid_start_time": "",
                            "bid_end_time": "",
                            "activity_id": 0,
                            "activity_name": "",
                            "one_minute": ""
                        }
                    ],
                    "free_deposit_task": "",
                    "estimated_bid_num": 10,
                    "is_cycle_activity": False
                }
            ],
            "count": 5
        }
    }
    for i in d.get("data").get("list"):
        sql = f"INSERT INTO `side-hustle`.`xianyu_product_info` (`id`, `xianyu_product_name`, `xianyu_product_status`, `device_type_code`, `sanmao_product_id`) " \
              f"VALUES ({i.get('product_id')}, '{i.get('title')}', '{i.get('status_text')}', '1', '');"
        print(sql)

if __name__ == '__main__':
    # 闲鱼产品()
    "2025-03-18 17:33:28"
    "2025-03-18 19:04:01"
    print(1742295841501-1742290408730)
    print(1742304621764-1742295979793)
    # print(int(time.time()))
        # print(i.get("Gid"))
        # print(i.get("Name"))
        # print(i.get("SalePrice"))
        # print(i.get("PriceA"))
        # print(i.get("PriceB"))
        # print(i.get("PriceC"))
        # print(i.get("State"))
