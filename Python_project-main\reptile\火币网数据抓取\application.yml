# 填写综合排名、收益率、收益额、跟单者人数
rank_type: "综合排名"
max_workers: 12
# 1 是慢抓取， 2 是快速抓取， 3 获取k线图，用 2 和 3 4综合获取
reptile_type: 3

proxies_type: 'socks5'     # 代理类型
proxies_http_port: "10809"    # 代理http端口
proxies_https_port: "10808"    # 代理https端口
exchange_name: "binance"    #

# 当replite_type = 3 时
symbol: "ETH/USDT"    # 分类
timeframe: "1s"     # 间隔时间 1天=1d、1分钟=1m、1秒钟=1s、1周=1w
start_time: "2025-05-08 00:00:00"     # 开始时间
end_time: "2025-05-10 00:00:00"        # 结束时间
#limit: 100     # 条数



# 当replite_type = 4 时
compute_yield:
  historical_leads_file_path: "C:\\Users\\<USER>\\Documents\\Code\\Python_project\\reptile\\火币网数据抓取\\2025042819历史带单数据.xlsx"
  start_time: ""
  end_time: ""
  timeframe: "1s"     # 间隔时间 1天=1d、1分钟=1m、1秒钟=1s、1周=1w
  max_workers: 5    # 值的越大，程序执行越快，出问题的概率越大。建议设置 1-5之间


logging:
  version: 1
  disable_existing_loggers: False
  formatters:
    simple:
      format: "%(asctime)s %(levelname)-8s [%(threadName)s] %(name)s %(filename)s: %(lineno)d: %(message)s"
  handlers:
    console:
      class: logging.StreamHandler
      level: INFO
      formatter: simple
    file:
      class: logging.handlers.TimedRotatingFileHandler
      filename: logs/app.log
      level: INFO
      formatter: simple
      encoding: UTF-8
      when: d
      interval: 1
      backupCount: 30
  root:
    level: INFO
    handlers: [file,console]